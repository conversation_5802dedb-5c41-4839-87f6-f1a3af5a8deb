/**
 * RSlamware Encryption Run Application
 * 
 * This application provides encryption and execution capabilities for RSlamware:
 * 1. Encrypts rslamware.tar.gz using AES-256-CBC
 * 2. Decrypts and runs launch files from encrypted archive
 * 3. Manages secure temporary directories
 */

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <memory>
#include <filesystem>
#include <cstdlib>
#include <unistd.h>
#include <sys/stat.h>
#include <sys/wait.h>

#include <openssl/evp.h>
#include <openssl/aes.h>
#include <openssl/rand.h>
#include <openssl/err.h>

namespace fs = std::filesystem;

class RSlamwareEncryption {
private:
    // Hard-coded encryption key (32 bytes for AES-256)
    static constexpr unsigned char ENCRYPTION_KEY[32] = {
        0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
        0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c,
        0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
        0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
    };
    
    static constexpr size_t IV_SIZE = 16;  // AES block size
    static constexpr size_t KEY_SIZE = 32; // AES-256 key size
    
public:
    /**
     * Encrypt a file using AES-256-CBC
     */
    bool encryptFile(const std::string& inputFile, const std::string& outputFile) {
        std::ifstream inFile(inputFile, std::ios::binary);
        if (!inFile) {
            std::cerr << "Error: Cannot open input file: " << inputFile << std::endl;
            return false;
        }
        
        std::ofstream outFile(outputFile, std::ios::binary);
        if (!outFile) {
            std::cerr << "Error: Cannot create output file: " << outputFile << std::endl;
            return false;
        }
        
        // Generate random IV
        unsigned char iv[IV_SIZE];
        if (RAND_bytes(iv, IV_SIZE) != 1) {
            std::cerr << "Error: Failed to generate IV" << std::endl;
            return false;
        }
        
        // Write IV to output file
        outFile.write(reinterpret_cast<char*>(iv), IV_SIZE);
        
        // Initialize encryption context
        EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
        if (!ctx) {
            std::cerr << "Error: Failed to create cipher context" << std::endl;
            return false;
        }
        
        if (EVP_EncryptInit_ex(ctx, EVP_aes_256_cbc(), nullptr, ENCRYPTION_KEY, iv) != 1) {
            std::cerr << "Error: Failed to initialize encryption" << std::endl;
            EVP_CIPHER_CTX_free(ctx);
            return false;
        }
        
        // Encrypt file in chunks
        const size_t BUFFER_SIZE = 4096;
        unsigned char inBuffer[BUFFER_SIZE];
        unsigned char outBuffer[BUFFER_SIZE + EVP_CIPHER_block_size(EVP_aes_256_cbc())];
        int outLen;
        
        while (inFile.read(reinterpret_cast<char*>(inBuffer), BUFFER_SIZE) || inFile.gcount() > 0) {
            int bytesRead = inFile.gcount();
            
            if (EVP_EncryptUpdate(ctx, outBuffer, &outLen, inBuffer, bytesRead) != 1) {
                std::cerr << "Error: Encryption failed" << std::endl;
                EVP_CIPHER_CTX_free(ctx);
                return false;
            }
            
            outFile.write(reinterpret_cast<char*>(outBuffer), outLen);
        }
        
        // Finalize encryption
        if (EVP_EncryptFinal_ex(ctx, outBuffer, &outLen) != 1) {
            std::cerr << "Error: Encryption finalization failed" << std::endl;
            EVP_CIPHER_CTX_free(ctx);
            return false;
        }
        
        outFile.write(reinterpret_cast<char*>(outBuffer), outLen);
        
        EVP_CIPHER_CTX_free(ctx);
        inFile.close();
        outFile.close();
        
        std::cout << "File encrypted successfully: " << outputFile << std::endl;
        return true;
    }
    
    /**
     * Decrypt data to memory (not to file for security)
     */
    std::vector<unsigned char> decryptToMemory(const std::string& inputFile) {
        std::ifstream inFile(inputFile, std::ios::binary);
        if (!inFile) {
            std::cerr << "Error: Cannot open encrypted file: " << inputFile << std::endl;
            return {};
        }
        
        // Read IV
        unsigned char iv[IV_SIZE];
        inFile.read(reinterpret_cast<char*>(iv), IV_SIZE);
        if (inFile.gcount() != IV_SIZE) {
            std::cerr << "Error: Cannot read IV from encrypted file" << std::endl;
            return {};
        }
        
        // Initialize decryption context
        EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
        if (!ctx) {
            std::cerr << "Error: Failed to create cipher context" << std::endl;
            return {};
        }
        
        if (EVP_DecryptInit_ex(ctx, EVP_aes_256_cbc(), nullptr, ENCRYPTION_KEY, iv) != 1) {
            std::cerr << "Error: Failed to initialize decryption" << std::endl;
            EVP_CIPHER_CTX_free(ctx);
            return {};
        }
        
        // Decrypt file in chunks
        const size_t BUFFER_SIZE = 4096;
        unsigned char inBuffer[BUFFER_SIZE];
        unsigned char outBuffer[BUFFER_SIZE + EVP_CIPHER_block_size(EVP_aes_256_cbc())];
        std::vector<unsigned char> decryptedData;
        int outLen;
        
        while (inFile.read(reinterpret_cast<char*>(inBuffer), BUFFER_SIZE) || inFile.gcount() > 0) {
            int bytesRead = inFile.gcount();
            
            if (EVP_DecryptUpdate(ctx, outBuffer, &outLen, inBuffer, bytesRead) != 1) {
                std::cerr << "Error: Decryption failed" << std::endl;
                EVP_CIPHER_CTX_free(ctx);
                return {};
            }
            
            decryptedData.insert(decryptedData.end(), outBuffer, outBuffer + outLen);
        }
        
        // Finalize decryption
        if (EVP_DecryptFinal_ex(ctx, outBuffer, &outLen) != 1) {
            std::cerr << "Error: Decryption finalization failed" << std::endl;
            EVP_CIPHER_CTX_free(ctx);
            return {};
        }
        
        decryptedData.insert(decryptedData.end(), outBuffer, outBuffer + outLen);
        
        EVP_CIPHER_CTX_free(ctx);
        inFile.close();
        
        std::cout << "File decrypted to memory successfully" << std::endl;
        return decryptedData;
    }
    
    /**
     * Create secure temporary directory
     */
    std::string createSecureTempDir() {
        uid_t userId = getuid();

#if 0
        std::string tempBase = "/run/user/" + std::to_string(userId) + "/rslamware";
#else

#endif
        // Create base directory if it doesn't exist
        if (!fs::exists(tempBase)) {
            if (!fs::create_directories(tempBase)) {
                std::cerr << "Error: Cannot create temp base directory: " << tempBase << std::endl;
                return "";
            }
        }
        
        // Set secure permissions (700 - owner only)
        if (chmod(tempBase.c_str(), S_IRWXU) != 0) {
            std::cerr << "Error: Cannot set permissions on temp directory" << std::endl;
            return "";
        }
        
        // Create unique subdirectory
        std::string tempDir = tempBase + "/rslamware_" + std::to_string(getpid());
        if (!fs::create_directories(tempDir)) {
            std::cerr << "Error: Cannot create temp directory: " << tempDir << std::endl;
            return "";
        }
        
        if (chmod(tempDir.c_str(), S_IRWXU) != 0) {
            std::cerr << "Error: Cannot set permissions on temp directory" << std::endl;
            return "";
        }
        
        std::cout << "Created secure temp directory: " << tempDir << std::endl;
        return tempDir;
    }

    /**
     * Extract tar.gz data to directory using libarchive
     */
    bool extractTarGz(const std::vector<unsigned char>& data, const std::string& extractDir) {
        // For now, use system tar command but write to memory first
        // In production, you should use libarchive for better security
        std::string tempTarFile = extractDir + "/temp.tar.gz";
        std::ofstream tempFile(tempTarFile, std::ios::binary);
        if (!tempFile) {
            std::cerr << "Error: Cannot create temporary tar file" << std::endl;
            return false;
        }

        tempFile.write(reinterpret_cast<const char*>(data.data()), data.size());
        tempFile.close();

        // Extract using tar command
        std::string extractCmd = "cd " + extractDir + " && tar -xzf temp.tar.gz";
        int result = system(extractCmd.c_str());

        // Remove temporary tar file immediately
        fs::remove(tempTarFile);

        if (result != 0) {
            std::cerr << "Error: Failed to extract tar.gz archive" << std::endl;
            return false;
        }

        std::cout << "Archive extracted successfully to: " << extractDir << std::endl;
        return true;
    }

    /**
     * Run launch files with ros2 launch
     */
    bool runLaunchFiles(const std::string& tempDir, const std::string& mode) {
        std::string launchDir = tempDir + "/launch";
        std::string configDir = tempDir + "/config";

        if (!fs::exists(launchDir) || !fs::exists(configDir)) {
            std::cerr << "Error: Launch or config directory not found in temp directory" << std::endl;
            return false;
        }

        // Set environment variables
        setenv("RSLAMWARE_TEMP_DIR", tempDir.c_str(), 1);
        setenv("RSLAMWARE_CONFIG_DIR", configDir.c_str(), 1);

        std::string launchFile;
        if (mode == "mapping") {
            launchFile = launchDir + "/rslamware_startup_mapping.launch.py";
        } else if (mode == "localization") {
            launchFile = launchDir + "/rslamware_startup_localization.launch.py";
        } else {
            std::cerr << "Error: Invalid mode. Use 'mapping' or 'localization'" << std::endl;
            return false;
        }

        if (!fs::exists(launchFile)) {
            std::cerr << "Error: Launch file not found: " << launchFile << std::endl;
            return false;
        }

        // Source ROS2 environment and run launch file
        std::string sourceCmd = "source /opt/ros/humble/setup.bash && source install/setup.bash";
        std::string launchCmd = "ros2 launch " + launchFile;
        std::string fullCmd = sourceCmd + " && " + launchCmd;

        std::cout << "Running launch command: " << fullCmd << std::endl;

        pid_t pid = fork();
        if (pid == 0) {
            // Child process
            execl("/bin/bash", "bash", "-c", fullCmd.c_str(), nullptr);
            exit(1); // Should not reach here
        } else if (pid > 0) {
            // Parent process - wait for launch to initialize
            std::cout << "Started launch process (PID: " << pid << ")" << std::endl;

            // Wait a bit for nodes to start and load configs
            sleep(10);

            // Check if process is still running
            int status;
            pid_t result = waitpid(pid, &status, WNOHANG);
            if (result == 0) {
                // Process is still running, which is expected for ROS2 launch
                std::cout << "Launch process is running successfully" << std::endl;
                return true;
            } else {
                std::cout << "Launch process finished with status: " << status << std::endl;
                return WIFEXITED(status) && WEXITSTATUS(status) == 0;
            }
        } else {
            std::cerr << "Error: Failed to fork process for launch" << std::endl;
            return false;
        }
    }

    /**
     * Clean up temporary directory
     */
    void cleanupTempDir(const std::string& tempDir) {
        if (!tempDir.empty() && fs::exists(tempDir)) {
            try {
                fs::remove_all(tempDir);
                std::cout << "Cleaned up temporary directory: " << tempDir << std::endl;
            } catch (const std::exception& e) {
                std::cerr << "Warning: Failed to clean up temp directory: " << e.what() << std::endl;
            }
        }
    }
};

void printUsage(const char* programName) {
    std::cout << "Usage: " << programName << " [OPTIONS]\n"
              << "Options:\n"
              << "  --encrypt [--input FILE] [--output FILE]  Encrypt rslamware.tar.gz to rslamware.enc\n"
              << "    --input FILE            Input tar.gz file (default: rslamware.tar.gz in same directory)\n"
              << "    --output FILE           Output encrypted file (default: rslamware.enc in same directory)\n"
              << "  --run [--file FILE] --mapping             Decrypt and run mapping mode\n"
              << "  --run [--file FILE] --localization        Decrypt and run localization mode\n"
              << "    --file FILE             Encrypted file to run (default: rslamware.enc in same directory)\n"
              << "  --help                    Show this help message\n"
              << std::endl;
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        printUsage(argv[0]);
        return 1;
    }

    // Initialize OpenSSL
    ERR_load_crypto_strings();
    OpenSSL_add_all_algorithms();

    RSlamwareEncryption encryption;

    std::string mode;
    std::string inputFile;
    std::string outputFile;
    std::string encryptedFile;
    bool encrypt = false;
    bool run = false;

    // Get executable directory for default file paths
    std::string execPath = argv[0];
    std::string execDir = fs::path(execPath).parent_path();
    if (execDir.empty()) execDir = ".";

    // Parse command line arguments
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];

        if (arg == "--encrypt") {
            encrypt = true;
        } else if (arg == "--run") {
            run = true;
        } else if (arg == "--mapping") {
            mode = "mapping";
        } else if (arg == "--localization") {
            mode = "localization";
        } else if (arg == "--input" && i + 1 < argc) {
            inputFile = argv[++i];
        } else if (arg == "--output" && i + 1 < argc) {
            outputFile = argv[++i];
        } else if (arg == "--file" && i + 1 < argc) {
            encryptedFile = argv[++i];
        } else if (arg == "--help") {
            printUsage(argv[0]);
            return 0;
        } else {
            std::cerr << "Error: Unknown argument: " << arg << std::endl;
            printUsage(argv[0]);
            return 1;
        }
    }

    // Set default file paths if not specified
    if (encrypt) {
        if (inputFile.empty()) {
            inputFile = execDir + "/rslamware.tar.gz";
        }
        if (outputFile.empty()) {
            outputFile = execDir + "/rslamware.enc";
        }
    }
    if (run) {
        if (encryptedFile.empty()) {
            encryptedFile = execDir + "/rslamware.enc";
        }
    }

    if (encrypt) {
        // Encryption mode
        if (!fs::exists(inputFile)) {
            std::cerr << "Error: Input file not found: " << inputFile << std::endl;
            return 1;
        }

        if (!encryption.encryptFile(inputFile, outputFile)) {
            std::cerr << "Error: Encryption failed" << std::endl;
            return 1;
        }

#if 0
        // Remove original file after successful encryption
        if (fs::remove(inputFile)) {
            std::cout << "Original file removed: " << inputFile << std::endl;
        } else {
            std::cerr << "Warning: Could not remove original file: " << inputFile << std::endl;
        }
#endif
        std::cout << "Encryption completed successfully!" << std::endl;
        std::cout << "Input: " << inputFile << std::endl;
        std::cout << "Output: " << outputFile << std::endl;

    } else if (run) {
        // Run mode
        if (mode.empty()) {
            std::cerr << "Error: Mode not specified. Use --mapping or --localization" << std::endl;
            printUsage(argv[0]);
            return 1;
        }

        if (!fs::exists(encryptedFile)) {
            std::cerr << "Error: Encrypted file not found: " << encryptedFile << std::endl;
            return 1;
        }

        // Decrypt to memory
        auto decryptedData = encryption.decryptToMemory(encryptedFile);
        if (decryptedData.empty()) {
            std::cerr << "Error: Decryption failed" << std::endl;
            return 1;
        }

        // Create secure temp directory
        std::string tempDir = encryption.createSecureTempDir();
        if (tempDir.empty()) {
            std::cerr << "Error: Failed to create secure temp directory" << std::endl;
            return 1;
        }

        // Extract to temp directory
        if (!encryption.extractTarGz(decryptedData, tempDir)) {
            encryption.cleanupTempDir(tempDir);
            return 1;
        }

        // Run launch files
        bool success = encryption.runLaunchFiles(tempDir, mode);

        // Note: We delay cleanup to allow launch files to fully initialize
        // The cleanup will happen when the process exits or after a delay
        if (success) {
            std::cout << "Launch files started successfully!" << std::endl;
            std::cout << "Temp directory: " << tempDir << std::endl;
            std::cout << "Note: Temp directory will be cleaned up when process exits" << std::endl;
        } else {
            // Clean up immediately on failure
            encryption.cleanupTempDir(tempDir);
            std::cerr << "Error: Failed to run launch files" << std::endl;
            return 1;
        }

    } else {
        std::cerr << "Error: No operation specified" << std::endl;
        printUsage(argv[0]);
        return 1;
    }

    // Cleanup OpenSSL
    EVP_cleanup();
    ERR_free_strings();

    return 0;
}
