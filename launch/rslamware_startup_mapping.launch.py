#!/usr/bin/env python3

from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource
import os

def generate_launch_description():
    # Get the directory where this launch file is located
    launch_dir = os.path.dirname(os.path.realpath(__file__))

    return LaunchDescription([
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(os.path.dirname(os.path.realpath(__file__)), 'rslamware.launch.py')
            )
        ),
        IncludeLaunchDescription(
            PythonLaunchDescriptionSource(
                os.path.join(os.path.dirname(os.path.realpath(__file__)), 'mapping.launch.py')
            )
        )
    ])
