{"C_Cpp.intelliSenseEngine": "default", "C_Cpp.default.includePath": ["${workspaceFolder}/**", "/opt/ros/humble/include/**", "/usr/include/**", "/usr/local/include/**", "/usr/include/x86_64-linux-gnu/**"], "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.intelliSenseMode": "linux-gcc-x64", "C_Cpp.default.compilerPath": "/usr/bin/gcc", "C_Cpp.default.browse.path": ["${workspaceFolder}", "/opt/ros/humble/include/**", "/usr/include/**", "/usr/local/include/**", "/usr/include/x86_64-linux-gnu/**"], "C_Cpp.default.defines": ["ROS2", "HUMBLE"], "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", "C_Cpp.default.forcedInclude": [], "C_Cpp.default.compileCommands": "${workspaceFolder}/build/compile_commands.json", "C_Cpp.default.cStandard": "c11", "C_Cpp.default.browse.limitSymbolsToIncludedHeaders": true, "C_Cpp.default.browse.databaseFilename": "${workspaceFolder}/.vscode/browse.c_cpp.db", "files.associations": {"functional": "cpp", "iostream": "cpp", "chrono": "cpp", "cctype": "cpp", "clocale": "cpp", "cmath": "cpp", "csignal": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "any": "cpp", "array": "cpp", "atomic": "cpp", "strstream": "cpp", "bit": "cpp", "bitset": "cpp", "cinttypes": "cpp", "codecvt": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "condition_variable": "cpp", "coroutine": "cpp", "cstdint": "cpp", "deque": "cpp", "forward_list": "cpp", "list": "cpp", "map": "cpp", "set": "cpp", "string": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "numeric": "cpp", "optional": "cpp", "random": "cpp", "ratio": "cpp", "regex": "cpp", "source_location": "cpp", "string_view": "cpp", "system_error": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "hash_map": "cpp", "hash_set": "cpp", "fstream": "cpp", "future": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "istream": "cpp", "limits": "cpp", "mutex": "cpp", "new": "cpp", "numbers": "cpp", "ostream": "cpp", "semaphore": "cpp", "shared_mutex": "cpp", "span": "cpp", "sstream": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "thread": "cpp", "cfenv": "cpp", "typeindex": "cpp", "typeinfo": "cpp", "valarray": "cpp", "variant": "cpp", "alignedvector3": "cpp", "charconv": "cpp", "expected": "cpp", "ranges": "cpp", "format": "cpp", "stdfloat": "cpp", "__nullptr": "cpp"}}